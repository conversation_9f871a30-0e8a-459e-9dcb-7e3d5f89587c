{
    "cmake.outputLogEncoding": "gbk",
    "C_Cpp.intelliSenseEngine": "disabled",
    "clangd.enable": true,
    "clangd.arguments": [
        "--clang-tidy",
        "--compile-commands-dir=build",
        "--fallback-style=Google",
        "--header-insertion-decorators",
        "--header-insertion=iwyu",
        "--log=verbose",
        "--pch-storage=memory",
        "--pretty",
        "--ranking-model=heuristics",
        "-j=12"
    ],
    "cmake.sourceDirectory": "C:/Users/<USER>/Desktop/TMP/ToppingProfessionalControlCenter",
}